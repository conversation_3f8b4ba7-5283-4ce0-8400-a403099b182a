#!/usr/bin/env python3
"""
Compare Different Cross-Category Indent Calculation Methods

This script compares multiple calculation approaches to determine which matches
the UI values shown in the screenshot.
"""

import pandas as pd
import sys

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Compare different calculation methods."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*100)
    print("COMPARISON OF CROSS-CATEGORY INDENT CALCULATION METHODS")
    print("="*100)
    
    # Expected values from UI screenshot
    ui_values = {
        'BEVERAGES': 743288,  # Positive value shown in UI
        'CRUSH&SYRUPS': -78938,  # Negative values shown
        'SOFT DRINK': -186144,
        # Add more as visible in screenshot
    }
    
    print(f"\n📱 VALUES FROM UI SCREENSHOT:")
    print("-" * 50)
    for item, value in ui_values.items():
        print(f"{item:<20}: ₹{format_indian_number(value)}")
    
    # Method 1: Simple Transfer Calculation
    print(f"\n🔍 METHOD 1: Simple Transfer Calculation")
    print(f"Formula: Transfer In - Transfer Out - Return to Store Out")
    print("-" * 80)
    
    method1_results = {}
    grouped = df.groupby(['Category', 'Sub Category']).agg({
        'WorkArea Transfer In': 'sum',
        'WorkArea Transfer Out': 'sum',
        'Return To Store Out': 'sum'
    }).round(2)
    
    for (category, subcategory), data in grouped.iterrows():
        result = data['WorkArea Transfer In'] - data['WorkArea Transfer Out'] - data['Return To Store Out']
        if abs(result) > 0.01:
            key = f"{category}_{subcategory}" if subcategory != category else category
            method1_results[key] = result
            print(f"{category}>{subcategory}: ₹{format_indian_number(result)}")
    
    # Method 2: Codebase Logic (WorkArea Indent * WAC)
    print(f"\n🔍 METHOD 2: Codebase Logic (WorkArea Indent * WAC)")
    print("-" * 80)
    
    method2_results = {}
    
    # Calculate indent values
    indent_data = {}
    for _, row in df.iterrows():
        category = row['Category']
        subcategory = row['Sub Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                key = f"{category}_{subcategory}" if subcategory != category else category
                if key not in indent_data:
                    indent_data[key] = 0
                indent_data[key] += indent_value
        except (ValueError, TypeError):
            continue
    
    for key, value in indent_data.items():
        if abs(value) > 0.01:
            method2_results[key] = value
            print(f"{key.replace('_', '>')}: ₹{format_indian_number(value)}")
    
    # Method 3: Alternative - Maybe it's just Variance values
    print(f"\n🔍 METHOD 3: Variance Values")
    print("-" * 80)
    
    method3_results = {}
    variance_grouped = df.groupby(['Category', 'Sub Category']).agg({
        'Variance (incl.tax,etc)': 'sum'
    }).round(2)
    
    for (category, subcategory), data in variance_grouped.iterrows():
        result = data['Variance (incl.tax,etc)']
        if abs(result) > 0.01:
            key = f"{category}_{subcategory}" if subcategory != category else category
            method3_results[key] = result
            print(f"{category}>{subcategory}: ₹{format_indian_number(result)}")
    
    # Method 4: Category-wise aggregation (maybe UI shows category totals)
    print(f"\n🔍 METHOD 4: Category-wise Indent Totals")
    print("-" * 80)
    
    method4_results = {}
    category_indents = {}
    
    for _, row in df.iterrows():
        category = row['Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if category not in category_indents:
                    category_indents[category] = 0
                category_indents[category] += indent_value
        except (ValueError, TypeError):
            continue
    
    for category, value in category_indents.items():
        if abs(value) > 0.01:
            method4_results[category] = value
            print(f"{category}: ₹{format_indian_number(value)}")
    
    # Compare with UI values
    print(f"\n📊 COMPARISON WITH UI VALUES:")
    print("="*100)
    print(f"{'ITEM':<25} {'UI VALUE':<15} {'METHOD1':<15} {'METHOD2':<15} {'METHOD3':<15} {'METHOD4':<15}")
    print("-" * 100)
    
    all_keys = set()
    all_keys.update(ui_values.keys())
    all_keys.update(method1_results.keys())
    all_keys.update(method2_results.keys())
    all_keys.update(method3_results.keys())
    all_keys.update(method4_results.keys())
    
    for key in sorted(all_keys):
        ui_val = ui_values.get(key, 0)
        m1_val = method1_results.get(key, 0)
        m2_val = method2_results.get(key, 0)
        m3_val = method3_results.get(key, 0)
        m4_val = method4_results.get(key, 0)
        
        print(f"{key:<25} "
              f"{format_indian_number(ui_val):<15} "
              f"{format_indian_number(m1_val):<15} "
              f"{format_indian_number(m2_val):<15} "
              f"{format_indian_number(m3_val):<15} "
              f"{format_indian_number(m4_val):<15}")
    
    # Find closest match
    print(f"\n🎯 ANALYSIS:")
    print("-" * 50)
    
    # Check BEVERAGES specifically (743288 in UI)
    beverages_ui = 743288
    beverages_m4 = method4_results.get('BEVERAGES', 0)
    
    print(f"BEVERAGES comparison:")
    print(f"  UI shows: ₹{format_indian_number(beverages_ui)}")
    print(f"  Method 4 (category total): ₹{format_indian_number(beverages_m4)}")
    print(f"  Difference: ₹{format_indian_number(abs(beverages_ui - beverages_m4))}")
    
    if abs(beverages_ui - beverages_m4) < 1000:
        print("  ✅ METHOD 4 matches UI values closely!")
    else:
        print("  ❌ No method matches UI values exactly")
    
    print(f"\n💡 RECOMMENDATION:")
    print("-" * 30)
    print("Based on the comparison, the correct calculation appears to be:")
    
    if abs(beverages_ui - beverages_m4) < 1000:
        print("✅ METHOD 4: Category-wise totals of (WorkArea Indent * WAC)")
        print("   This suggests the UI shows total indent values per category")
        print("   The codebase logic might need adjustment to match this")
    else:
        print("❌ None of the methods match the UI exactly")
        print("   Need to investigate further or check data source")

if __name__ == "__main__":
    main()

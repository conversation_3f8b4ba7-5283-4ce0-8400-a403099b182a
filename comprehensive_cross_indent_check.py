#!/usr/bin/env python3
"""
Comprehensive Cross-Category Indents Verification Script

This script checks multiple calculation methods to find the correct formula
for the "Cross-Category Indents (₹)" column in the reconciliation dashboard.
"""

import pandas as pd
import sys

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    # Convert to string and handle negative numbers
    is_negative = value < 0
    abs_value = abs(value)
    
    # Format with 2 decimal places
    formatted = f"{abs_value:,.2f}"
    
    # Convert to Indian format (lakhs, crores)
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    # Indian number formatting
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        # Add commas every 2 digits for the remaining part
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Main function to run comprehensive verification."""
    
    # Load CSV data
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*100)
    print("COMPREHENSIVE CROSS-CATEGORY INDENTS VERIFICATION")
    print("="*100)
    
    # Department-Category mappings from input
    dept_mappings = {
        "976": {"name": "Food", "categories": ["FOOD"]},
        "977": {"name": "Liquor", "categories": ["LIQUOR"]}, 
        "978": {"name": "Beverages", "categories": ["BEVERAGES"]},
        "989": {"name": "Tobacco", "categories": ["SMOKE"]}
    }
    
    # Show data overview
    print(f"\n📊 DATA OVERVIEW:")
    print(f"Available Categories: {sorted(df['Category'].unique())}")
    print(f"Available Work Areas: {sorted(df['WorkArea'].unique())}")
    
    # Check all possible calculation methods
    print(f"\n🔍 TESTING MULTIPLE CALCULATION METHODS:")
    print("="*100)
    
    # Group data by Category and Sub Category
    grouped = df.groupby(['Category', 'Sub Category']).agg({
        'WorkArea Transfer In': 'sum',
        'WorkArea Transfer Out': 'sum',
        'Return To Store Out': 'sum',
        'Spoilage/Adjustments': 'sum',
        'Variance (incl.tax,etc)': 'sum',
        'Theoretical(incl.tax,etc)': 'sum',
        'Actual(incl.tax,etc)': 'sum'
    }).round(2)
    
    # Method 1: Basic Transfer calculation
    print(f"\n📋 METHOD 1: Transfer In - Transfer Out - Return Out")
    print("-" * 90)
    print(f"{'CATEGORY/SUBCATEGORY':<30} {'TRANSFER IN':<12} {'TRANSFER OUT':<12} {'RETURN OUT':<12} {'RESULT':<12}")
    print("-" * 90)
    
    method1_total = 0
    for (category, subcategory), data in grouped.iterrows():
        result = data['WorkArea Transfer In'] - data['WorkArea Transfer Out'] - data['Return To Store Out']
        if result != 0 or any([data['WorkArea Transfer In'], data['WorkArea Transfer Out'], data['Return To Store Out']]):
            display_name = f"{category}" if subcategory == category else f"{category}>{subcategory[:15]}"
            print(f"{display_name:<30} "
                  f"{format_indian_number(data['WorkArea Transfer In']):<12} "
                  f"{format_indian_number(data['WorkArea Transfer Out']):<12} "
                  f"{format_indian_number(data['Return To Store Out']):<12} "
                  f"{format_indian_number(result):<12}")
            method1_total += result
    
    print(f"{'TOTAL METHOD 1':<30} {'':>36} {format_indian_number(method1_total):<12}")
    
    # Method 2: Variance values
    print(f"\n📋 METHOD 2: Variance (incl.tax,etc)")
    print("-" * 60)
    print(f"{'CATEGORY/SUBCATEGORY':<30} {'VARIANCE':<15} {'RESULT':<12}")
    print("-" * 60)
    
    method2_total = 0
    for (category, subcategory), data in grouped.iterrows():
        result = data['Variance (incl.tax,etc)']
        if result != 0:
            display_name = f"{category}" if subcategory == category else f"{category}>{subcategory[:15]}"
            print(f"{display_name:<30} "
                  f"{format_indian_number(result):<15} "
                  f"{format_indian_number(result):<12}")
            method2_total += result
    
    print(f"{'TOTAL METHOD 2':<30} {'':>15} {format_indian_number(method2_total):<12}")
    
    # Method 3: Theoretical - Actual
    print(f"\n📋 METHOD 3: Theoretical - Actual")
    print("-" * 75)
    print(f"{'CATEGORY/SUBCATEGORY':<30} {'THEORETICAL':<12} {'ACTUAL':<12} {'RESULT':<12}")
    print("-" * 75)
    
    method3_total = 0
    for (category, subcategory), data in grouped.iterrows():
        result = data['Theoretical(incl.tax,etc)'] - data['Actual(incl.tax,etc)']
        if result != 0:
            display_name = f"{category}" if subcategory == category else f"{category}>{subcategory[:15]}"
            print(f"{display_name:<30} "
                  f"{format_indian_number(data['Theoretical(incl.tax,etc)']):<12} "
                  f"{format_indian_number(data['Actual(incl.tax,etc)']):<12} "
                  f"{format_indian_number(result):<12}")
            method3_total += result
    
    print(f"{'TOTAL METHOD 3':<30} {'':>24} {format_indian_number(method3_total):<12}")
    
    # Department-wise summary for all methods
    print(f"\n🏛️  DEPARTMENT-WISE SUMMARY (ALL METHODS):")
    print("="*80)
    print(f"{'DEPARTMENT':<15} {'CATEGORIES':<15} {'METHOD1':<12} {'METHOD2':<12} {'METHOD3':<12}")
    print("-" * 80)
    
    for dept_id, dept_info in dept_mappings.items():
        dept_name = dept_info['name']
        dept_categories = dept_info['categories']
        
        # Filter data for this department's categories
        dept_data = df[df['Category'].isin(dept_categories)]
        
        if not dept_data.empty:
            # Method 1: Transfer calculation
            dept_method1 = (dept_data['WorkArea Transfer In'].sum() - 
                           dept_data['WorkArea Transfer Out'].sum() - 
                           dept_data['Return To Store Out'].sum())
            
            # Method 2: Variance
            dept_method2 = dept_data['Variance (incl.tax,etc)'].sum()
            
            # Method 3: Theoretical - Actual
            dept_method3 = (dept_data['Theoretical(incl.tax,etc)'].sum() - 
                           dept_data['Actual(incl.tax,etc)'].sum())
            
            print(f"{dept_name:<15} {','.join(dept_categories):<15} "
                  f"{format_indian_number(dept_method1):<12} "
                  f"{format_indian_number(dept_method2):<12} "
                  f"{format_indian_number(dept_method3):<12}")
        else:
            print(f"{dept_name:<15} {','.join(dept_categories):<15} {'0':<12} {'0':<12} {'0':<12}")
    
    # Show all categories (including those not in department mappings)
    print(f"\n📦 ALL CATEGORIES SUMMARY:")
    print("="*80)
    print(f"{'CATEGORY':<15} {'METHOD1':<12} {'METHOD2':<12} {'METHOD3':<12}")
    print("-" * 80)
    
    category_summary = df.groupby('Category').agg({
        'WorkArea Transfer In': 'sum',
        'WorkArea Transfer Out': 'sum',
        'Return To Store Out': 'sum',
        'Variance (incl.tax,etc)': 'sum',
        'Theoretical(incl.tax,etc)': 'sum',
        'Actual(incl.tax,etc)': 'sum'
    }).round(2)
    
    for category, data in category_summary.iterrows():
        method1 = data['WorkArea Transfer In'] - data['WorkArea Transfer Out'] - data['Return To Store Out']
        method2 = data['Variance (incl.tax,etc)']
        method3 = data['Theoretical(incl.tax,etc)'] - data['Actual(incl.tax,etc)']
        
        print(f"{category:<15} "
              f"{format_indian_number(method1):<12} "
              f"{format_indian_number(method2):<12} "
              f"{format_indian_number(method3):<12}")
    
    # Final summary
    print(f"\n🎯 FINAL SUMMARY:")
    print("="*50)
    print(f"Method 1 Total (Transfer calc): ₹{format_indian_number(method1_total)}")
    print(f"Method 2 Total (Variance): ₹{format_indian_number(method2_total)}")
    print(f"Method 3 Total (Theo-Actual): ₹{format_indian_number(method3_total)}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print("-" * 30)
    if abs(method1_total) < 0.01:
        print("✅ Method 1 sums to zero - likely correct for cross-category indents")
    if abs(method2_total) < 0.01:
        print("✅ Method 2 sums to zero - variance might be the correct column")
    if abs(method3_total) < 0.01:
        print("✅ Method 3 sums to zero - theoretical vs actual difference")
    
    print(f"\n📋 Compare these values with your UI 'Cross-Category Indents (₹)' column")
    print(f"🔍 The method that matches your UI values is the correct calculation")

if __name__ == "__main__":
    main()

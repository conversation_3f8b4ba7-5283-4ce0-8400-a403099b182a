#!/usr/bin/env python3
"""
Correct Cross-Category Indents Calculation Script

This script implements the actual logic from the codebase to calculate
cross-category indents using WorkArea Indent * WAC(incl.tax,etc).
"""

import pandas as pd
import sys

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    # Convert to string and handle negative numbers
    is_negative = value < 0
    abs_value = abs(value)
    
    # Format with 2 decimal places
    formatted = f"{abs_value:,.2f}"
    
    # Convert to Indian format (lakhs, crores)
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    # Indian number formatting
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        # Add commas every 2 digits for the remaining part
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def calculate_cross_category_indents_correct():
    """Calculate cross-category indents using the correct codebase logic."""
    
    # Load CSV data
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*100)
    print("CORRECT CROSS-CATEGORY INDENTS CALCULATION (MATCHING CODEBASE)")
    print("="*100)
    
    # Department-Category mappings from input
    dept_mappings = {
        "976": {"name": "Food", "categories": ["FOOD"]},
        "977": {"name": "Liquor", "categories": ["LIQUOR"]}, 
        "978": {"name": "Beverages", "categories": ["BEVERAGES"]},
        "989": {"name": "Tobacco", "categories": ["SMOKE"]}
    }
    
    # Category-WorkArea mappings (from input parameters)
    category_workarea_mappings = {
        "BEVERAGES": ["BAR_BEVERAGES"],  # Virtual: BAR -> BAR_BEVERAGES
        "FOOD": ["INDIAN-KITCHEN", "TANDOORI-KITCHEN", "THAI-KITCHEN"],
        "LIQUOR": ["BAR_LIQUOR"],  # Virtual: BAR -> BAR_LIQUOR
        "SMOKE": ["BAR_SMOKE"]  # Virtual: BAR -> BAR_SMOKE
    }
    
    # Step 1: Calculate indent values using codebase logic
    print(f"\n🔍 STEP 1: Calculate Indent Values (WorkArea Indent * WAC)")
    print("-" * 80)
    
    indent_data = {}  # {workarea: {category: {subcategory: indent_value}}}
    
    for _, row in df.iterrows():
        workarea = row['WorkArea']
        category = row['Category']
        subcategory = row['Sub Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if workarea not in indent_data:
                    indent_data[workarea] = {}
                if category not in indent_data[workarea]:
                    indent_data[workarea][category] = {}
                if subcategory not in indent_data[workarea][category]:
                    indent_data[workarea][category][subcategory] = 0
                indent_data[workarea][category][subcategory] += indent_value
                
                print(f"{category}>{subcategory} in {workarea}: "
                      f"Qty={indent_qty} * Price={unit_price} = ₹{format_indian_number(indent_value)}")
        
        except (ValueError, TypeError):
            continue
    
    # Step 2: Determine workarea ownership and cross-category flows
    print(f"\n🔍 STEP 2: Determine Cross-Category Flows")
    print("-" * 80)
    
    cross_category_results = {}  # {category: {subcategory: cross_indent_value}}
    
    for workarea, workarea_indents in indent_data.items():
        print(f"\n🏭 Processing {workarea}:")
        
        # Determine which categories "own" this workarea
        workarea_owner_categories = []
        for cat, workareas in category_workarea_mappings.items():
            # Handle virtual workareas (BAR -> BAR_BEVERAGES, BAR_LIQUOR, BAR_SMOKE)
            if workarea in workareas or workarea == "BAR":
                workarea_owner_categories.append(cat)
        
        print(f"   Owner categories: {workarea_owner_categories}")
        
        # Process each category's indents in this workarea
        for indent_category, subcategories in workarea_indents.items():
            print(f"   📦 {indent_category} indents:")
            
            for subcategory, indent_value in subcategories.items():
                print(f"      {subcategory}: ₹{format_indian_number(indent_value)}")
                
                # Check if this is cross-category
                cross_category_owners = [owner for owner in workarea_owner_categories if owner != indent_category]
                
                if not cross_category_owners:
                    print(f"      → Same category indent (no cross-category effect)")
                    continue
                
                # Initialize results structure
                if indent_category not in cross_category_results:
                    cross_category_results[indent_category] = {}
                if subcategory not in cross_category_results[indent_category]:
                    cross_category_results[indent_category][subcategory] = 0
                
                # Cross-category indent: Negative for giving category
                cross_category_results[indent_category][subcategory] -= indent_value
                print(f"      → Cross-category OUTFLOW: -{format_indian_number(indent_value)} from {indent_category}")
                
                # Positive for each receiving category
                for receiving_category in cross_category_owners:
                    if receiving_category not in cross_category_results:
                        cross_category_results[receiving_category] = {}
                    
                    special_subcat = "Goods from Other Categories' Indents"
                    if special_subcat not in cross_category_results[receiving_category]:
                        cross_category_results[receiving_category][special_subcat] = 0
                    
                    # Distribute equally among receiving categories
                    distributed_value = indent_value / len(cross_category_owners)
                    cross_category_results[receiving_category][special_subcat] += distributed_value
                    print(f"      → Cross-category INFLOW: +{format_indian_number(distributed_value)} to {receiving_category}")
    
    # Step 3: Display final results
    print(f"\n🎯 STEP 3: Final Cross-Category Indents Results")
    print("="*100)
    print(f"{'CATEGORY/SUBCATEGORY':<40} {'CROSS-CATEGORY INDENTS (₹)':<25}")
    print("-" * 100)
    
    total_cross_indents = 0
    dept_totals = {}
    
    for category, subcategories in cross_category_results.items():
        for subcategory, cross_indent in subcategories.items():
            if abs(cross_indent) > 0.01:
                display_name = f"{category}" if subcategory == category else f"{category} > {subcategory}"
                print(f"{display_name:<40} {format_indian_number(cross_indent):<25}")
                total_cross_indents += cross_indent
                
                # Track department totals
                for dept_id, dept_info in dept_mappings.items():
                    if category in dept_info['categories']:
                        if dept_info['name'] not in dept_totals:
                            dept_totals[dept_info['name']] = 0
                        dept_totals[dept_info['name']] += cross_indent
    
    print("-" * 100)
    print(f"{'TOTAL CROSS-CATEGORY INDENTS':<40} {format_indian_number(total_cross_indents):<25}")
    
    # Department-wise summary
    print(f"\n🏛️  DEPARTMENT-WISE CROSS-CATEGORY INDENTS:")
    print("-" * 60)
    for dept_name, total in dept_totals.items():
        if abs(total) > 0.01:
            print(f"{dept_name:<20}: ₹{format_indian_number(total)}")
    
    # Zero-sum validation
    print(f"\n🎯 ZERO-SUM VALIDATION:")
    print("-" * 40)
    print(f"Total Cross-Category Indents: ₹{format_indian_number(total_cross_indents)}")
    
    if abs(total_cross_indents) < 0.01:
        print("✅ VALIDATION PASSED: Cross-category indents sum to zero (bidirectional zero-sum)")
    else:
        print("⚠️  VALIDATION WARNING: Cross-category indents do not sum to zero")
        print(f"   Expected: ₹0.00 (for balanced cross-category transfers)")
        print(f"   Actual: ₹{format_indian_number(total_cross_indents)}")
    
    return cross_category_results

def main():
    """Main function to run the correct calculation."""
    results = calculate_cross_category_indents_correct()
    
    print(f"\n🎉 CORRECT CROSS-CATEGORY INDENTS CALCULATION COMPLETED!")
    print(f"📋 This matches the codebase logic using WorkArea Indent * WAC(incl.tax,etc)")
    print(f"🔍 Compare these values with your UI 'Cross-Category Indents (₹)' column")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Current UI Values Comparison

Compare the current UI values from the screenshots with our calculations.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Compare current UI values with calculations."""
    
    print("="*100)
    print("CURRENT UI VALUES COMPARISON")
    print("="*100)
    
    # Current UI values from the new screenshots
    current_ui_values = {
        # LIQUOR section (first screenshot)
        'VEGETABLES': 216197,  # Positive
        'LIQUOR': -1185054,   # Negative (total for LIQUOR category)
        'APERITIF': 0,        # Zero (not visible in UI)
        'BAR': -48356,
        'BEER': -84136,
        'BRANDY': -20530,
        'BREEZER': -14324,
        'DRAUGHT BEER': -341505,
        'GIN': -153424,
        'LIQUEUR': -95916,
        'RUM': -48479,
        'TEQUILA': -228583,
        'VODKA': -173002,
        'WHISKEY': -553319,
        'WINE': -113086,
        'Goods from Other Categories Indents (LIQUOR)': 716006,  # Positive
        'SMOKE': 0,
        'CIGRATE': -53800,
        'Goods from Other Categories Indents (SMOKE)': 716006,  # Positive
        
        # BEVERAGES section (second screenshot)
        'BEVERAGES': 625812,  # Total for BEVERAGES category
        'CRUSH&SYRUPS': -78938,
        'SOFT DRINK': 186144,  # Positive
        'Goods from Other Categories Indents (BEVERAGES)': 716006,  # Positive
        
        # FOOD section (second screenshot)
        'FOOD': -2675444,     # Negative total
        'BAKERY BISCUIT&WAFER': -3819,
        'BAR_FOOD': 0,
        'BEEF PORK& MUTTON': -531971,
        'BIG-SIS': 0,
        'CIGAR': 0,
        'DESSERT': 0,
        'FISH & SEA FOOD': -164441,
        'FROZEN': -27028,
        'FRUITS': -51490,
        'GROCERY': -713935,
        'ICE CREAM': -1022,
        'IMP VEGETABLES': -45576,
        'IMPGROCERY': -128473,
        'KITCHEN': 0,
        'MILK & DAIRY': -319494,
    }
    
    # My calculated values (from previous runs)
    my_calculated_values = {
        'CRUSH&SYRUPS': -78938.36,
        'SOFT DRINK': 186144.25,  # Should be positive (native)
        'BAR': -48355.59,
        'BEER': -84136.08,
        'BRANDY': -20530.03,
        'BREEZER': -14323.56,
        'DRAUGHT BEER': -341505.50,
        'GIN': -153423.53,
        'LIQUEUR': -95916.22,
        'RUM': -48478.43,
        'TEQUILA': -228583.38,
        'VODKA': -173002.12,
        'WHISKEY': -553319.23,
        'WINE': -113086.32,
    }
    
    print(f"\n📊 INDIVIDUAL ITEMS COMPARISON:")
    print("="*80)
    print(f"{'ITEM':<25} {'UI VALUE':<15} {'CALCULATED':<15} {'MATCH':<8}")
    print("-" * 80)
    
    matches = 0
    total_items = 0
    
    for item in ['CRUSH&SYRUPS', 'SOFT DRINK', 'BAR', 'BEER', 'BRANDY', 'BREEZER', 
                 'DRAUGHT BEER', 'GIN', 'LIQUEUR', 'RUM', 'TEQUILA', 'VODKA', 'WHISKEY', 'WINE']:
        if item in current_ui_values and item in my_calculated_values:
            ui_val = current_ui_values[item]
            calc_val = my_calculated_values[item]
            match = abs(ui_val - calc_val) < 1000
            if match:
                matches += 1
            total_items += 1
            
            print(f"{item:<25} {format_indian_number(ui_val):<15} {format_indian_number(calc_val):<15} {'✅' if match else '❌'}")
    
    print(f"\n🎯 KEY OBSERVATIONS:")
    print("="*50)
    
    # Check the "Goods from Other Categories' Indents" values
    goods_from_others = current_ui_values.get('Goods from Other Categories Indents (LIQUOR)', 0)
    print(f"1. 'Goods from Other Categories Indents': ₹{format_indian_number(goods_from_others)}")
    print(f"   - This appears consistently across LIQUOR, SMOKE, and BEVERAGES sections")
    print(f"   - This is the key to understanding the category totals")
    
    # Calculate what BEVERAGES total should be
    beverages_items = current_ui_values['CRUSH&SYRUPS'] + current_ui_values['SOFT DRINK']
    beverages_goods = current_ui_values.get('Goods from Other Categories Indents (BEVERAGES)', 0)
    calculated_beverages_total = beverages_items + beverages_goods
    ui_beverages_total = current_ui_values['BEVERAGES']
    
    print(f"\n2. BEVERAGES Total Calculation:")
    print(f"   - CRUSH&SYRUPS: ₹{format_indian_number(current_ui_values['CRUSH&SYRUPS'])}")
    print(f"   - SOFT DRINK: ₹{format_indian_number(current_ui_values['SOFT DRINK'])}")
    print(f"   - Goods from Others: ₹{format_indian_number(beverages_goods)}")
    print(f"   - Calculated Total: ₹{format_indian_number(calculated_beverages_total)}")
    print(f"   - UI Total: ₹{format_indian_number(ui_beverages_total)}")
    print(f"   - Match: {'✅' if abs(calculated_beverages_total - ui_beverages_total) < 1000 else '❌'}")
    
    # Calculate what LIQUOR total should be
    liquor_individual_items = sum([
        current_ui_values['BAR'], current_ui_values['BEER'], current_ui_values['BRANDY'],
        current_ui_values['BREEZER'], current_ui_values['DRAUGHT BEER'], current_ui_values['GIN'],
        current_ui_values['LIQUEUR'], current_ui_values['RUM'], current_ui_values['TEQUILA'],
        current_ui_values['VODKA'], current_ui_values['WHISKEY'], current_ui_values['WINE']
    ])
    liquor_goods = current_ui_values.get('Goods from Other Categories Indents (LIQUOR)', 0)
    calculated_liquor_total = liquor_individual_items + liquor_goods
    ui_liquor_total = current_ui_values['LIQUOR']
    
    print(f"\n3. LIQUOR Total Calculation:")
    print(f"   - Sum of individual items: ₹{format_indian_number(liquor_individual_items)}")
    print(f"   - Goods from Others: ₹{format_indian_number(liquor_goods)}")
    print(f"   - Calculated Total: ₹{format_indian_number(calculated_liquor_total)}")
    print(f"   - UI Total: ₹{format_indian_number(ui_liquor_total)}")
    print(f"   - Match: {'✅' if abs(calculated_liquor_total - ui_liquor_total) < 1000 else '❌'}")
    
    print(f"\n4. New Items Observed:")
    print(f"   - VEGETABLES: ₹{format_indian_number(current_ui_values['VEGETABLES'])} (positive)")
    print(f"   - CIGRATE: ₹{format_indian_number(current_ui_values['CIGRATE'])} (negative)")
    print(f"   - Multiple FOOD items with negative values")
    
    print(f"\n📈 SUMMARY:")
    print("="*40)
    print(f"Individual items matching: {matches}/{total_items}")
    print(f"Key insight: 'Goods from Other Categories Indents' = ₹{format_indian_number(goods_from_others)}")
    print(f"This value appears to be the same across all categories, suggesting")
    print(f"a uniform distribution of cross-category benefits.")
    
    if matches == total_items:
        print(f"\n🎉 All individual items match perfectly!")
        print(f"The cross-category indents calculation is working correctly.")
    else:
        print(f"\n⚠️  {total_items - matches} items still need adjustment.")

if __name__ == "__main__":
    main()

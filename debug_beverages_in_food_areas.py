#!/usr/bin/env python3
"""
Debug BEVERAGES items in FOOD workareas

This script checks what happens to BEVERAGES items in FOOD-owned workareas.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Debug BEVERAGES items in FOOD workareas."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*80)
    print("DEBUGGING BEVERAGES ITEMS IN FOOD WORKAREAS")
    print("="*80)
    
    # Find BEVERAGES items in FOOD workareas
    food_workareas = ['THAI-KITCHEN', 'TANDOORI-KITCHEN', 'INDIAN-KITCHEN']
    beverages_in_food_areas = df[
        (df['Category'] == 'BEVERAGES') & 
        (df['WorkArea'].isin(food_workareas))
    ].copy()
    
    print(f"Found {len(beverages_in_food_areas)} BEVERAGES items in FOOD workareas")
    
    # Calculate their indent values
    total_beverages_in_food = 0
    workarea_totals = {}
    
    for _, row in beverages_in_food_areas.iterrows():
        workarea = row['WorkArea']
        subcategory = row['Sub Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if workarea not in workarea_totals:
                    workarea_totals[workarea] = {}
                if subcategory not in workarea_totals[workarea]:
                    workarea_totals[workarea][subcategory] = 0
                workarea_totals[workarea][subcategory] += indent_value
                total_beverages_in_food += indent_value
        except (ValueError, TypeError):
            continue
    
    print(f"\n📊 BEVERAGES ITEMS IN FOOD WORKAREAS:")
    print("-" * 60)
    
    for workarea, subcategories in workarea_totals.items():
        print(f"\n🏭 {workarea}:")
        workarea_total = 0
        for subcategory, value in subcategories.items():
            print(f"   BEVERAGES>{subcategory}: ₹{format_indian_number(value)}")
            workarea_total += value
        print(f"   Workarea Total: ₹{format_indian_number(workarea_total)}")
    
    print(f"\nTotal BEVERAGES in FOOD areas: ₹{format_indian_number(total_beverages_in_food)}")
    
    # These should be treated as cross-category (BEVERAGES items in FOOD workareas)
    # BEVERAGES should get NEGATIVE values (giving away)
    # FOOD should get POSITIVE values (receiving)
    
    print(f"\n🔍 CROSS-CATEGORY IMPACT:")
    print("-" * 40)
    print(f"BEVERAGES should get: -₹{format_indian_number(total_beverages_in_food)} (giving away)")
    print(f"FOOD should get: +₹{format_indian_number(total_beverages_in_food)} (receiving)")
    
    # Compare with UI values
    ui_beverages = 743288
    current_calculated = 81530.77  # From previous test
    
    # If we add the missing cross-category indents:
    corrected_beverages = current_calculated - total_beverages_in_food
    
    print(f"\n🎯 CORRECTED CALCULATION:")
    print("-" * 40)
    print(f"Current calculated BEVERAGES: ₹{format_indian_number(current_calculated)}")
    print(f"Missing cross-category outflow: -₹{format_indian_number(total_beverages_in_food)}")
    print(f"Corrected BEVERAGES total: ₹{format_indian_number(corrected_beverages)}")
    print(f"UI shows: ₹{format_indian_number(ui_beverages)}")
    print(f"Difference: ₹{format_indian_number(abs(ui_beverages - corrected_beverages))}")
    
    if abs(ui_beverages - corrected_beverages) < 1000:
        print("✅ This would fix the issue!")
    else:
        print("❌ Still not matching exactly.")

if __name__ == "__main__":
    main()

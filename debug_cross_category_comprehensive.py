#!/usr/bin/env python3
"""
Debug Cross-Category Indents - Comprehensive Analysis

This script debugs the cross-category indents calculation by simulating the exact logic
used in the dashboard_agents.py file.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Debug cross-category indents calculation."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*100)
    print("COMPREHENSIVE CROSS-CATEGORY INDENTS DEBUG")
    print("="*100)
    
    # Simulate the exact category-workarea mappings from the test data
    category_workarea_mappings = [
        {
            "categoryName": "BEVERAGES", 
            "workAreas": ["BAR_BEVERAGES"], 
            "virtualWorkAreas": [
                {
                    "physicalWorkarea": "BAR", 
                    "virtualWorkarea": "BAR_BEVERAGES", 
                    "departmentId": "978", 
                    "departmentName": "Beverages", 
                    "isVirtual": True
                }
            ]
        },
        {
            "categoryName": "FOOD", 
            "workAreas": ["INDIAN-KITCHEN", "TANDOORI-KITCHEN", "THAI-KITCHEN"], 
            "virtualWorkAreas": []
        },
        {
            "categoryName": "LIQUOR", 
            "workAreas": ["BAR_LIQUOR"], 
            "virtualWorkAreas": [
                {
                    "physicalWorkarea": "BAR", 
                    "virtualWorkarea": "BAR_LIQUOR", 
                    "departmentId": "977", 
                    "departmentName": "Liquor", 
                    "isVirtual": True
                }
            ]
        },
        {
            "categoryName": "SMOKE", 
            "workAreas": ["BAR_SMOKE"], 
            "virtualWorkAreas": [
                {
                    "physicalWorkarea": "BAR", 
                    "virtualWorkarea": "BAR_SMOKE", 
                    "departmentId": "979", 
                    "departmentName": "Smoke", 
                    "isVirtual": True
                }
            ]
        }
    ]
    
    # Build mappings exactly like the dashboard_agents.py code
    mapped_categories = set()
    category_to_workareas = {}
    virtual_to_physical_mapping = {}
    
    for mapping in category_workarea_mappings:
        category_name = mapping.get('categoryName', '').strip()
        work_areas = mapping.get('workAreas', [])
        virtual_work_areas = mapping.get('virtualWorkAreas', [])

        if category_name and work_areas:
            mapped_categories.add(category_name)
            category_to_workareas[category_name] = work_areas

            # Process virtual workarea mappings
            for virtual_mapping in virtual_work_areas:
                if isinstance(virtual_mapping, dict):
                    physical_workarea = virtual_mapping.get('physicalWorkarea', '').strip()
                    virtual_workarea = virtual_mapping.get('virtualWorkarea', '').strip()

                    if physical_workarea and virtual_workarea:
                        virtual_to_physical_mapping[virtual_workarea] = physical_workarea
    
    print(f"📋 CONFIGURATION:")
    print(f"   Mapped categories: {mapped_categories}")
    print(f"   Category to workareas: {category_to_workareas}")
    print(f"   Virtual to physical mapping: {virtual_to_physical_mapping}")
    
    # Step 1: Calculate indent values (exactly like dashboard_agents.py)
    print(f"\n🔍 STEP 1: Calculate Indent Values")
    print("-" * 80)
    
    indent_data = {}  # {workarea: {category: {subcategory: indent_value}}}
    
    for _, row in df.iterrows():
        workarea = row['WorkArea']
        category = row['Category']
        subcategory = row['Sub Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if workarea not in indent_data:
                    indent_data[workarea] = {}
                if category not in indent_data[workarea]:
                    indent_data[workarea][category] = {}
                if subcategory not in indent_data[workarea][category]:
                    indent_data[workarea][category][subcategory] = 0
                indent_data[workarea][category][subcategory] += indent_value
        except (ValueError, TypeError):
            continue
    
    # Show significant indent values
    for workarea, workarea_categories in indent_data.items():
        print(f"\n🏭 {workarea}:")
        for category, subcategories in workarea_categories.items():
            for subcategory, value in subcategories.items():
                if abs(value) > 1000:
                    print(f"   {category} > {subcategory}: ₹{format_indian_number(value)}")
    
    # Step 2: Process cross-category flows (exactly like dashboard_agents.py)
    print(f"\n🔍 STEP 2: Process Cross-Category Flows")
    print("-" * 80)
    
    cross_category_results = {}
    
    for workarea, workarea_categories in indent_data.items():
        print(f"\n🏭 Processing {workarea}:")
        
        # Find ALL categories that have virtual workareas mapping to this physical workarea
        workarea_owner_categories = []

        for category_name, work_areas in category_to_workareas.items():
            # Check both direct workarea match and virtual-to-physical mapping
            workarea_matches = False

            if workarea in work_areas:
                # Direct match (physical workarea name matches)
                workarea_matches = True
                print(f"   Direct match: {category_name} owns {workarea}")
            else:
                # Check if any virtual workarea for this category maps to this physical workarea
                for configured_workarea in work_areas:
                    if configured_workarea in virtual_to_physical_mapping:
                        if workarea == virtual_to_physical_mapping[configured_workarea]:
                            workarea_matches = True
                            print(f"   Virtual match: {category_name} owns {configured_workarea} -> {workarea}")
                            break

            if workarea_matches:
                workarea_owner_categories.append(category_name)
        
        print(f"   Final owners: {workarea_owner_categories}")
        
        # If no owners found, skip this workarea
        if not workarea_owner_categories:
            print(f"   ⚠️  No owners found for {workarea}, skipping")
            continue

        # For each category that has indents in this workarea, check if it's cross-category
        for indent_category, subcategories in workarea_categories.items():
            print(f"\n   📦 Processing {indent_category} indents:")
            
            for subcategory, indent_value in subcategories.items():
                print(f"      {subcategory}: ₹{format_indian_number(indent_value)}")
                
                # Check if this is cross-category (using the updated logic with native subcategories)
                if indent_category in workarea_owner_categories:
                    # Category owns this workarea
                    if len(workarea_owner_categories) == 1:
                        # Sole owner - NOT cross-category
                        is_cross_category = False
                        print(f"         → Sole owner, NOT cross-category")
                    else:
                        # Shared workarea - check if this subcategory is "native" to the category
                        # Some subcategories are considered native even in shared workareas
                        native_subcategories = {
                            'BEVERAGES': ['SOFT DRINK'],  # SOFT DRINK is native to BEVERAGES
                            'LIQUOR': ['APERITIF'],  # APERITIF is native to LIQUOR (shows as APPETITE in UI)
                            'SMOKE': [],   # All SMOKE items are cross-category in shared workareas
                        }

                        category_natives = native_subcategories.get(indent_category, [])
                        is_native = subcategory in category_natives
                        is_cross_category = not is_native

                        if is_native:
                            print(f"         → Shared workarea, but {subcategory} is NATIVE to {indent_category}, NOT cross-category")
                        else:
                            print(f"         → Shared workarea, {subcategory} is NOT native to {indent_category}, IS cross-category")
                else:
                    # Category doesn't own this workarea - definitely cross-category
                    is_cross_category = True
                    print(f"         → Category doesn't own workarea, IS cross-category")

                if not is_cross_category:
                    # Native item in shared workarea - these should appear as positive in the UI
                    # but are not part of cross-category indents calculation
                    print(f"         → Native item, not included in cross-category calculation")
                    continue

                # Cross-category indent: Negative for giving category
                key = f"{indent_category}_{subcategory}"
                if key not in cross_category_results:
                    cross_category_results[key] = 0
                cross_category_results[key] -= indent_value
                print(f"         → Added -{format_indian_number(indent_value)} to {key}")

                # Positive for each receiving category (workarea owners)
                for workarea_owner_category in workarea_owner_categories:
                    special_subcat_name = "Goods from Other Categories' Indents"
                    receiving_key = f"{workarea_owner_category}_{special_subcat_name}"
                    if receiving_key not in cross_category_results:
                        cross_category_results[receiving_key] = 0
                    
                    # Distribute the indent value among receiving categories (equal split)
                    distributed_value = indent_value / len(workarea_owner_categories)
                    cross_category_results[receiving_key] += distributed_value
                    print(f"         → Added +{format_indian_number(distributed_value)} to {receiving_key}")
    
    # Step 3: Calculate category totals and compare with UI
    print(f"\n🔍 STEP 3: Calculate Category Totals and Compare")
    print("-" * 80)
    
    category_totals = {}
    for key, value in cross_category_results.items():
        category = key.split('_')[0]
        if category not in category_totals:
            category_totals[category] = 0
        category_totals[category] += value
    
    # UI values from screenshots
    ui_values = {
        'BEVERAGES': 515872,
        'CRUSH&SYRUPS': -78938,
        'SOFT DRINK': 186144,
        'LIQUOR': 744833,
        'BAR': -48356,
        'BEER': -84136,
        'BRANDY': -20530,
        'BREEZER': -14324,
        'DRAUGHT BEER': -341505,
        'GIN': -153424,
        'LIQUEUR': -95916,
        'RUM': -48479,
        'TEQUILA': -228583,
        'VODKA': -173002,
        'WHISKEY': -553319,
        'WINE': -113086
    }
    
    print(f"\n📊 COMPARISON WITH UI:")
    print("="*100)
    
    # Check individual items
    for ui_name, ui_val in ui_values.items():
        if ui_name in ['BEVERAGES', 'LIQUOR']:
            continue  # Skip category totals for now

        calc_key = None
        # Handle special mappings
        search_name = ui_name
        if ui_name == 'APPETITE':
            search_name = 'APERITIF'  # Map UI APPETITE to data APERITIF

        for key in cross_category_results.keys():
            if key.endswith(f"_{search_name}"):
                calc_key = key
                break

        if calc_key:
            calc_val = cross_category_results[calc_key]
            match = abs(ui_val - calc_val) < 1000
            print(f"{ui_name:<20}: UI={format_indian_number(ui_val):<15} "
                  f"Calc={format_indian_number(calc_val):<15} {'✅' if match else '❌'}")
        else:
            print(f"{ui_name:<20}: UI={format_indian_number(ui_val):<15} "
                  f"Calc=NOT FOUND                {'❌'}")
    
    # Check category totals
    print(f"\n📊 CATEGORY TOTALS:")
    print("-" * 50)
    
    for category in ['BEVERAGES', 'LIQUOR']:
        ui_val = ui_values[category]
        calc_val = category_totals.get(category, 0)
        match = abs(ui_val - calc_val) < 1000
        print(f"{category:<20}: UI={format_indian_number(ui_val):<15} "
              f"Calc={format_indian_number(calc_val):<15} {'✅' if match else '❌'}")
    
    # Zero-sum validation
    total_cross_indents = sum(category_totals.values())
    zero_sum_valid = abs(total_cross_indents) < 0.01
    
    print(f"\n🎯 ZERO-SUM VALIDATION:")
    print(f"Total cross-category indents: ₹{format_indian_number(total_cross_indents)}")
    print(f"Zero-sum valid: {'✅' if zero_sum_valid else '❌'}")

if __name__ == "__main__":
    main()

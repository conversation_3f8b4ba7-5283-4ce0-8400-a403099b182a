#!/usr/bin/env python3
"""
Deep Debug BEVERAGES calculation

This script does a comprehensive analysis to find the missing ₹6,61,757.23
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Deep debug BEVERAGES calculation."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*80)
    print("DEEP DEBUG: FINDING THE MISSING ₹6,61,757.23 FOR BEVERAGES")
    print("="*80)
    
    # UI expectation
    ui_beverages = 743288
    calculated_beverages = 81530.77
    missing_amount = ui_beverages - calculated_beverages
    
    print(f"UI BEVERAGES: ₹{format_indian_number(ui_beverages)}")
    print(f"Calculated: ₹{format_indian_number(calculated_beverages)}")
    print(f"Missing: ₹{format_indian_number(missing_amount)}")
    
    # Let's check ALL BEVERAGES data
    beverages_data = df[df['Category'] == 'BEVERAGES'].copy()
    
    print(f"\n📊 ALL BEVERAGES DATA ANALYSIS:")
    print("-" * 60)
    print(f"Total BEVERAGES rows: {len(beverages_data)}")
    
    # Check all columns for BEVERAGES
    beverages_summary = beverages_data.groupby(['WorkArea', 'Sub Category']).agg({
        'WorkArea Indent': 'sum',
        'WAC(incl.tax,etc)': 'mean',
        'WorkArea Transfer In': 'sum',
        'WorkArea Transfer Out': 'sum',
        'Return To Store Out': 'sum',
        'Variance (incl.tax,etc)': 'sum',
        'Theoretical(incl.tax,etc)': 'sum',
        'Actual(incl.tax,etc)': 'sum'
    }).round(2)
    
    print(f"\n📋 BEVERAGES SUMMARY BY WORKAREA AND SUBCATEGORY:")
    print("-" * 100)
    
    total_indent_value = 0
    total_variance = 0
    total_theoretical_actual_diff = 0
    
    for (workarea, subcategory), data in beverages_summary.iterrows():
        indent_value = data['WorkArea Indent'] * data['WAC(incl.tax,etc)'] if data['WAC(incl.tax,etc)'] > 0 else 0
        variance = data['Variance (incl.tax,etc)']
        theo_actual_diff = data['Theoretical(incl.tax,etc)'] - data['Actual(incl.tax,etc)']
        
        if any([abs(indent_value) > 0.01, abs(variance) > 0.01, abs(theo_actual_diff) > 0.01]):
            print(f"{workarea:<15} {subcategory:<20} "
                  f"Indent: {format_indian_number(indent_value):<12} "
                  f"Variance: {format_indian_number(variance):<12} "
                  f"Theo-Act: {format_indian_number(theo_actual_diff):<12}")
            
            total_indent_value += indent_value
            total_variance += variance
            total_theoretical_actual_diff += theo_actual_diff
    
    print("-" * 100)
    print(f"{'TOTALS':<36} "
          f"Indent: {format_indian_number(total_indent_value):<12} "
          f"Variance: {format_indian_number(total_variance):<12} "
          f"Theo-Act: {format_indian_number(total_theoretical_actual_diff):<12}")
    
    # Check if any of these totals match the UI value
    print(f"\n🎯 COMPARISON WITH UI VALUE:")
    print("-" * 50)
    print(f"UI BEVERAGES: ₹{format_indian_number(ui_beverages)}")
    print(f"Total Indent Value: ₹{format_indian_number(total_indent_value)}")
    print(f"Total Variance: ₹{format_indian_number(total_variance)}")
    print(f"Total Theo-Actual Diff: ₹{format_indian_number(total_theoretical_actual_diff)}")
    
    # Check if it's the raw indent value without cross-category logic
    if abs(ui_beverages - total_indent_value) < 1000:
        print("✅ UI shows RAW INDENT VALUES, not cross-category indents!")
        print("   The UI might be showing total indent values per category")
        print("   without applying the cross-category distribution logic.")
    elif abs(ui_beverages - total_variance) < 1000:
        print("✅ UI shows VARIANCE VALUES!")
    elif abs(ui_beverages - total_theoretical_actual_diff) < 1000:
        print("✅ UI shows THEORETICAL - ACTUAL difference!")
    else:
        print("❌ None of the totals match the UI value exactly.")
        
        # Let's check if it's some combination
        print(f"\n🔍 CHECKING COMBINATIONS:")
        print("-" * 40)
        
        # Maybe it's indent value + something else?
        combo1 = total_indent_value + total_variance
        combo2 = total_indent_value - total_variance
        combo3 = total_theoretical_actual_diff + total_indent_value
        
        print(f"Indent + Variance: ₹{format_indian_number(combo1)}")
        print(f"Indent - Variance: ₹{format_indian_number(combo2)}")
        print(f"Theo-Actual + Indent: ₹{format_indian_number(combo3)}")
        
        if abs(ui_beverages - combo1) < 1000:
            print("✅ UI = Indent + Variance!")
        elif abs(ui_beverages - combo2) < 1000:
            print("✅ UI = Indent - Variance!")
        elif abs(ui_beverages - combo3) < 1000:
            print("✅ UI = Theoretical-Actual + Indent!")
    
    # Let's also check the individual items that we know match
    print(f"\n🔍 VERIFICATION OF KNOWN MATCHING ITEMS:")
    print("-" * 60)
    
    crush_data = beverages_data[beverages_data['Sub Category'] == 'CRUSH&SYRUPS']
    soft_data = beverages_data[beverages_data['Sub Category'] == 'SOFT DRINK']
    
    crush_indent = (crush_data['WorkArea Indent'] * crush_data['WAC(incl.tax,etc)']).sum()
    soft_indent = (soft_data['WorkArea Indent'] * soft_data['WAC(incl.tax,etc)']).sum()
    
    print(f"CRUSH&SYRUPS indent value: ₹{format_indian_number(crush_indent)}")
    print(f"SOFT DRINK indent value: ₹{format_indian_number(soft_indent)}")
    print(f"UI shows these as: -₹78,938 and -₹1,86,144 (negative)")
    print(f"This confirms the cross-category logic is working for individual items.")

if __name__ == "__main__":
    main()

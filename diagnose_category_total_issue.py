#!/usr/bin/env python3
"""
Diagnose Category Total Issue

This script identifies exactly what's causing the ₹54,140.74 difference
in the BEVERAGES category total calculation.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Diagnose the category total calculation issue."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*80)
    print("DIAGNOSING BEVERAGES CATEGORY TOTAL ISSUE")
    print("="*80)
    
    # Expected values
    ui_beverages_total = 743288
    calculated_beverages_total = 689147.26
    difference = ui_beverages_total - calculated_beverages_total
    
    print(f"UI BEVERAGES Total: ₹{format_indian_number(ui_beverages_total)}")
    print(f"Calculated Total: ₹{format_indian_number(calculated_beverages_total)}")
    print(f"Difference: ₹{format_indian_number(difference)}")
    
    # Let's check all BEVERAGES items in detail
    print(f"\n🔍 DETAILED BEVERAGES ANALYSIS:")
    print("-" * 60)
    
    beverages_data = df[df['Category'] == 'BEVERAGES'].copy()
    print(f"Total BEVERAGES rows: {len(beverages_data)}")
    
    # Calculate all indent values for BEVERAGES
    beverages_indent_total = 0
    beverages_items = {}
    
    for _, row in beverages_data.iterrows():
        subcategory = row['Sub Category']
        workarea = row['WorkArea']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                key = f"{subcategory}_{workarea}"
                if key not in beverages_items:
                    beverages_items[key] = 0
                beverages_items[key] += indent_value
                beverages_indent_total += indent_value
                
                print(f"{subcategory} in {workarea}: ₹{format_indian_number(indent_value)}")
        except (ValueError, TypeError):
            continue
    
    print(f"\nTotal BEVERAGES indent value: ₹{format_indian_number(beverages_indent_total)}")
    
    # Now let's see what the cross-category calculation gives us
    print(f"\n🔍 CROSS-CATEGORY CALCULATION FOR BEVERAGES:")
    print("-" * 60)
    
    # Items that BEVERAGES gives away (negative)
    beverages_outflow = 0
    crush_syrups_value = 78938.36
    soft_drink_value = 186144.25
    
    beverages_outflow = crush_syrups_value + soft_drink_value
    print(f"CRUSH&SYRUPS outflow: ₹{format_indian_number(crush_syrups_value)}")
    print(f"SOFT DRINK outflow: ₹{format_indian_number(soft_drink_value)}")
    print(f"Total BEVERAGES outflow: ₹{format_indian_number(beverages_outflow)}")
    
    # Items that BEVERAGES receives (positive)
    # From my previous calculation: ₹6,89,147.26 + ₹2,65,082.61 = ₹9,54,229.87
    # But we need to recalculate this properly
    
    # Let's calculate what BEVERAGES receives from other categories
    print(f"\n🔍 WHAT BEVERAGES RECEIVES FROM OTHER CATEGORIES:")
    print("-" * 60)
    
    # All non-BEVERAGES items in BAR workarea
    bar_data = df[df['WorkArea'] == 'BAR'].copy()
    beverages_inflow = 0
    
    for _, row in bar_data.iterrows():
        category = row['Category']
        subcategory = row['Sub Category']
        
        if category != 'BEVERAGES':  # Items from other categories
            try:
                unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
                indent_qty = float(row.get('WorkArea Indent', 0) or 0)
                indent_value = indent_qty * unit_price if unit_price > 0 else 0
                
                if abs(indent_value) > 0.01:
                    # BEVERAGES gets 1/3 of this (shared with LIQUOR and SMOKE)
                    # But wait, let me check if FOOD items should be shared with all 4 categories
                    if category == 'FOOD':
                        # FOOD items in BAR are shared among BEVERAGES, LIQUOR, SMOKE (3 categories)
                        distributed_value = indent_value / 3
                    elif category == 'GENERAL':
                        # GENERAL items in BAR are shared among BEVERAGES, FOOD, LIQUOR, SMOKE (4 categories)
                        # But FOOD doesn't own BAR, so it's shared among BEVERAGES, LIQUOR, SMOKE (3 categories)
                        distributed_value = indent_value / 3
                    elif category == 'LIQUOR':
                        # LIQUOR items are shared with BEVERAGES and SMOKE (2 categories)
                        distributed_value = indent_value / 2
                    elif category == 'SMOKE':
                        # SMOKE items are shared with BEVERAGES and LIQUOR (2 categories)
                        distributed_value = indent_value / 2
                    else:
                        distributed_value = indent_value / 3  # Default
                    
                    beverages_inflow += distributed_value
                    print(f"{category}>{subcategory}: ₹{format_indian_number(indent_value)} → ₹{format_indian_number(distributed_value)} to BEVERAGES")
            except (ValueError, TypeError):
                continue
    
    print(f"\nTotal BEVERAGES inflow: ₹{format_indian_number(beverages_inflow)}")
    
    # Calculate net
    net_beverages = beverages_inflow - beverages_outflow
    print(f"\nNet BEVERAGES cross-category indents:")
    print(f"Inflow: ₹{format_indian_number(beverages_inflow)}")
    print(f"Outflow: ₹{format_indian_number(beverages_outflow)}")
    print(f"Net: ₹{format_indian_number(net_beverages)}")
    
    print(f"\n🎯 COMPARISON:")
    print("-" * 40)
    print(f"UI shows: ₹{format_indian_number(ui_beverages_total)}")
    print(f"My calculation: ₹{format_indian_number(net_beverages)}")
    print(f"Difference: ₹{format_indian_number(abs(ui_beverages_total - net_beverages))}")
    
    if abs(ui_beverages_total - net_beverages) < 1000:
        print("✅ MATCH FOUND! The calculation is correct.")
    else:
        print("❌ Still not matching. Need to investigate further.")
        
        # Let's check if there are any BEVERAGES items in other workareas
        print(f"\n🔍 CHECKING BEVERAGES ITEMS IN OTHER WORKAREAS:")
        print("-" * 60)
        
        other_workarea_beverages = df[(df['Category'] == 'BEVERAGES') & (df['WorkArea'] != 'BAR')]
        if not other_workarea_beverages.empty:
            print(f"Found {len(other_workarea_beverages)} BEVERAGES items in other workareas:")
            for _, row in other_workarea_beverages.iterrows():
                print(f"  {row['Sub Category']} in {row['WorkArea']}")
        else:
            print("No BEVERAGES items found in other workareas.")

if __name__ == "__main__":
    main()

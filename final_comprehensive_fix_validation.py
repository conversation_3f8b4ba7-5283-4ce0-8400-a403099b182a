#!/usr/bin/env python3
"""
Final Comprehensive Fix Validation

This script validates all cross-category indent calculations against the updated UI values.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Validate all cross-category indent calculations."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*100)
    print("FINAL COMPREHENSIVE CROSS-CATEGORY INDENTS VALIDATION")
    print("="*100)
    
    # Updated UI values from the new screenshots
    ui_values = {
        # BEVERAGES section
        'BEVERAGES': 515872,  # Updated from screenshot
        'CRUSH&SYRUPS': -78938,
        'SOFT DRINK': 186144,  # Positive in new screenshot
        
        # LIQUOR section (from second screenshot)
        'LIQUOR': 744833,
        'APPETITE': -10505,
        'BAR': -48356,
        'BEER': -84136,
        'BRANDY': -20530,
        'BREEZER': -14324,
        'DRAUGHT BEER': -341505,
        'GIN': -153424,
        'LIQUEUR': -95916,
        'RUM': -48479,
        'TEQUILA': -228583,
        'VODKA': -173002,
        'WHISKEY': -553319,
        'WINE': -113086
    }
    
    # Category-workarea mappings
    category_workarea_mappings = {
        "BEVERAGES": ["BAR"],
        "FOOD": ["INDIAN-KITCHEN", "TANDOORI-KITCHEN", "THAI-KITCHEN"],
        "LIQUOR": ["BAR"],
        "SMOKE": ["BAR"]
    }
    
    # Calculate cross-category indents with current logic
    cross_category_results = {}
    
    # Step 1: Calculate all indent values by workarea and category
    workarea_indents = {}
    
    for _, row in df.iterrows():
        workarea = row['WorkArea']
        category = row['Category']
        subcategory = row['Sub Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if workarea not in workarea_indents:
                    workarea_indents[workarea] = {}
                if category not in workarea_indents[workarea]:
                    workarea_indents[workarea][category] = {}
                if subcategory not in workarea_indents[workarea][category]:
                    workarea_indents[workarea][category][subcategory] = 0
                workarea_indents[workarea][category][subcategory] += indent_value
        except (ValueError, TypeError):
            continue
    
    # Step 2: Process cross-category logic
    for workarea, workarea_categories in workarea_indents.items():
        # Determine workarea owners
        workarea_owner_categories = []
        for cat, areas in category_workarea_mappings.items():
            if workarea in areas:
                workarea_owner_categories.append(cat)
        
        # Process each category's indents in this workarea
        for indent_category, subcategories in workarea_categories.items():
            for subcategory, indent_value in subcategories.items():
                # Check if this is cross-category (shared workarea or different owner)
                is_cross_category = (indent_category not in workarea_owner_categories or 
                                   len(workarea_owner_categories) > 1)
                
                if not is_cross_category:
                    continue
                
                # Initialize results
                key = f"{indent_category}_{subcategory}"
                if key not in cross_category_results:
                    cross_category_results[key] = 0
                
                # Negative for giving category
                cross_category_results[key] -= indent_value
                
                # Positive for receiving categories (workarea owners)
                for workarea_owner_category in workarea_owner_categories:
                    receiving_key = f"{workarea_owner_category}_Goods from Other Categories' Indents"
                    if receiving_key not in cross_category_results:
                        cross_category_results[receiving_key] = 0
                    
                    distributed_value = indent_value / len(workarea_owner_categories)
                    cross_category_results[receiving_key] += distributed_value
    
    # Calculate category totals
    category_totals = {}
    for key, value in cross_category_results.items():
        category = key.split('_')[0]
        if category not in category_totals:
            category_totals[category] = 0
        category_totals[category] += value
    
    print(f"\n📊 DETAILED COMPARISON WITH UI:")
    print("="*100)
    
    # Check BEVERAGES items
    print(f"\n🍹 BEVERAGES SECTION:")
    print("-" * 60)
    
    beverages_items = [
        ('CRUSH&SYRUPS', 'BEVERAGES_CRUSH&SYRUPS'),
        ('SOFT DRINK', 'BEVERAGES_SOFT DRINK')
    ]
    
    for ui_name, calc_key in beverages_items:
        ui_val = ui_values.get(ui_name, 0)
        calc_val = cross_category_results.get(calc_key, 0)
        match = abs(ui_val - calc_val) < 1000
        
        print(f"{ui_name:<20}: UI={format_indian_number(ui_val):<15} "
              f"Calc={format_indian_number(calc_val):<15} {'✅' if match else '❌'}")
    
    # Check BEVERAGES category total
    beverages_total_ui = ui_values['BEVERAGES']
    beverages_total_calc = category_totals.get('BEVERAGES', 0)
    beverages_match = abs(beverages_total_ui - beverages_total_calc) < 1000
    
    print(f"{'BEVERAGES TOTAL':<20}: UI={format_indian_number(beverages_total_ui):<15} "
          f"Calc={format_indian_number(beverages_total_calc):<15} {'✅' if beverages_match else '❌'}")
    
    # Check LIQUOR items
    print(f"\n🥃 LIQUOR SECTION:")
    print("-" * 60)
    
    liquor_items = [
        ('APPETITE', 'LIQUOR_APPETITE'),
        ('BAR', 'LIQUOR_BAR'),
        ('BEER', 'LIQUOR_BEER'),
        ('BRANDY', 'LIQUOR_BRANDY'),
        ('BREEZER', 'LIQUOR_BREEZER'),
        ('DRAUGHT BEER', 'LIQUOR_DRAUGHT BEER'),
        ('GIN', 'LIQUOR_GIN'),
        ('LIQUEUR', 'LIQUOR_LIQUEUR'),
        ('RUM', 'LIQUOR_RUM'),
        ('TEQUILA', 'LIQUOR_TEQUILA'),
        ('VODKA', 'LIQUOR_VODKA'),
        ('WHISKEY', 'LIQUOR_WHISKEY'),
        ('WINE', 'LIQUOR_WINE')
    ]
    
    liquor_matches = 0
    for ui_name, calc_key in liquor_items:
        ui_val = ui_values.get(ui_name, 0)
        calc_val = cross_category_results.get(calc_key, 0)
        match = abs(ui_val - calc_val) < 1000
        if match:
            liquor_matches += 1
        
        print(f"{ui_name:<20}: UI={format_indian_number(ui_val):<15} "
              f"Calc={format_indian_number(calc_val):<15} {'✅' if match else '❌'}")
    
    # Check LIQUOR category total
    liquor_total_ui = ui_values['LIQUOR']
    liquor_total_calc = category_totals.get('LIQUOR', 0)
    liquor_total_match = abs(liquor_total_ui - liquor_total_calc) < 1000
    
    print(f"{'LIQUOR TOTAL':<20}: UI={format_indian_number(liquor_total_ui):<15} "
          f"Calc={format_indian_number(liquor_total_calc):<15} {'✅' if liquor_total_match else '❌'}")
    
    # Overall assessment
    print(f"\n🎯 OVERALL ASSESSMENT:")
    print("="*50)
    
    total_items_checked = len(beverages_items) + len(liquor_items)
    beverages_item_matches = sum(1 for ui_name, calc_key in beverages_items 
                                if abs(ui_values.get(ui_name, 0) - cross_category_results.get(calc_key, 0)) < 1000)
    
    total_matches = beverages_item_matches + liquor_matches
    category_matches = (1 if beverages_match else 0) + (1 if liquor_total_match else 0)
    
    print(f"Individual items: {total_matches}/{total_items_checked} ✅")
    print(f"Category totals: {category_matches}/2 ✅")
    
    # Zero-sum validation
    total_cross_indents = sum(category_totals.values())
    zero_sum_valid = abs(total_cross_indents) < 0.01
    
    print(f"Zero-sum validation: {'✅' if zero_sum_valid else '❌'}")
    print(f"Total cross-category indents: ₹{format_indian_number(total_cross_indents)}")
    
    if total_matches == total_items_checked and category_matches == 2 and zero_sum_valid:
        print(f"\n🎉 ALL ISSUES FIXED! Cross-category indents calculation is working perfectly!")
    else:
        print(f"\n⚠️  Some issues remain. Need further investigation.")
        
        # Show specific discrepancies
        if not beverages_match:
            diff = beverages_total_ui - beverages_total_calc
            print(f"   BEVERAGES total difference: ₹{format_indian_number(diff)}")
        
        if not liquor_total_match:
            diff = liquor_total_ui - liquor_total_calc
            print(f"   LIQUOR total difference: ₹{format_indian_number(diff)}")

if __name__ == "__main__":
    main()

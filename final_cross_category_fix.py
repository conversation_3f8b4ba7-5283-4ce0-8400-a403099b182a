#!/usr/bin/env python3
"""
Final Cross-Category Indents Fix

This script implements the correct cross-category indents logic that matches the UI exactly.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Implement the final cross-category indents fix."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*100)
    print("FINAL CROSS-CATEGORY INDENTS FIX")
    print("="*100)
    
    # Category-workarea mappings with virtual workareas
    category_workarea_mappings = [
        {
            "categoryName": "BEVERAGES", 
            "workAreas": ["BAR_BEVERAGES"], 
            "virtualWorkAreas": [
                {"physicalWorkarea": "BAR", "virtualWorkarea": "BAR_BEVERAGES"}
            ]
        },
        {
            "categoryName": "FOOD", 
            "workAreas": ["INDIAN-KITCHEN", "TANDOORI-KITCHEN", "THAI-KITCHEN"], 
            "virtualWorkAreas": []
        },
        {
            "categoryName": "LIQUOR", 
            "workAreas": ["BAR_LIQUOR"], 
            "virtualWorkAreas": [
                {"physicalWorkarea": "BAR", "virtualWorkarea": "BAR_LIQUOR"}
            ]
        },
        {
            "categoryName": "SMOKE", 
            "workAreas": ["BAR_SMOKE"], 
            "virtualWorkAreas": [
                {"physicalWorkarea": "BAR", "virtualWorkarea": "BAR_SMOKE"}
            ]
        }
    ]
    
    # Build mappings
    category_to_workareas = {}
    virtual_to_physical_mapping = {}
    
    for mapping in category_workarea_mappings:
        category_name = mapping.get('categoryName', '').strip()
        work_areas = mapping.get('workAreas', [])
        virtual_work_areas = mapping.get('virtualWorkAreas', [])

        if category_name and work_areas:
            category_to_workareas[category_name] = work_areas

            for virtual_mapping in virtual_work_areas:
                if isinstance(virtual_mapping, dict):
                    physical_workarea = virtual_mapping.get('physicalWorkarea', '').strip()
                    virtual_workarea = virtual_mapping.get('virtualWorkarea', '').strip()
                    if physical_workarea and virtual_workarea:
                        virtual_to_physical_mapping[virtual_workarea] = physical_workarea
    
    # Native subcategories (items that appear as positive even in shared workareas)
    native_subcategories = {
        'BEVERAGES': ['SOFT DRINK'],
        'LIQUOR': ['APERITIF'],  # Maps to APPETITE in UI
        'SMOKE': [],
    }
    
    # Calculate indent values
    indent_data = {}
    native_items = {}  # Track native items separately
    
    for _, row in df.iterrows():
        workarea = row['WorkArea']
        category = row['Category']
        subcategory = row['Sub Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if workarea not in indent_data:
                    indent_data[workarea] = {}
                if category not in indent_data[workarea]:
                    indent_data[workarea][category] = {}
                if subcategory not in indent_data[workarea][category]:
                    indent_data[workarea][category][subcategory] = 0
                indent_data[workarea][category][subcategory] += indent_value
        except (ValueError, TypeError):
            continue
    
    # Process cross-category flows
    cross_category_results = {}
    
    for workarea, workarea_categories in indent_data.items():
        # Find workarea owners
        workarea_owner_categories = []
        for category_name, work_areas in category_to_workareas.items():
            workarea_matches = False
            if workarea in work_areas:
                workarea_matches = True
            else:
                for configured_workarea in work_areas:
                    if configured_workarea in virtual_to_physical_mapping:
                        if workarea == virtual_to_physical_mapping[configured_workarea]:
                            workarea_matches = True
                            break
            if workarea_matches:
                workarea_owner_categories.append(category_name)
        
        if not workarea_owner_categories:
            continue
        
        # Process each category's indents
        for indent_category, subcategories in workarea_categories.items():
            for subcategory, indent_value in subcategories.items():
                
                # Determine if this is cross-category
                if indent_category in workarea_owner_categories:
                    if len(workarea_owner_categories) == 1:
                        # Sole owner - not cross-category
                        is_cross_category = False
                    else:
                        # Shared workarea - check if native
                        category_natives = native_subcategories.get(indent_category, [])
                        is_native = subcategory in category_natives
                        is_cross_category = not is_native
                else:
                    # Category doesn't own workarea - definitely cross-category
                    is_cross_category = True
                
                if not is_cross_category:
                    # Native item - track separately for UI display
                    key = f"{indent_category}_{subcategory}"
                    if key not in native_items:
                        native_items[key] = 0
                    native_items[key] += indent_value
                    continue
                
                # Cross-category indent: Negative for giving category
                key = f"{indent_category}_{subcategory}"
                if key not in cross_category_results:
                    cross_category_results[key] = 0
                cross_category_results[key] -= indent_value
                
                # Positive for receiving categories
                for workarea_owner_category in workarea_owner_categories:
                    receiving_key = f"{workarea_owner_category}_Goods from Other Categories' Indents"
                    if receiving_key not in cross_category_results:
                        cross_category_results[receiving_key] = 0
                    distributed_value = indent_value / len(workarea_owner_categories)
                    cross_category_results[receiving_key] += distributed_value
    
    # Combine cross-category and native results for UI display
    ui_results = {}
    ui_results.update(cross_category_results)
    ui_results.update(native_items)
    
    # Calculate category totals
    category_totals = {}
    for key, value in ui_results.items():
        category = key.split('_')[0]
        if category not in category_totals:
            category_totals[category] = 0
        category_totals[category] += value
    
    # UI values for comparison
    ui_values = {
        'BEVERAGES': 515872,
        'CRUSH&SYRUPS': -78938,
        'SOFT DRINK': 186144,
        'LIQUOR': 744833,
        'APPETITE': -10505,  # Maps to APERITIF
        'BAR': -48356,
        'BEER': -84136,
        'BRANDY': -20530,
        'BREEZER': -14324,
        'DRAUGHT BEER': -341505,
        'GIN': -153424,
        'LIQUEUR': -95916,
        'RUM': -48479,
        'TEQUILA': -228583,
        'VODKA': -173002,
        'WHISKEY': -553319,
        'WINE': -113086
    }
    
    print(f"\n📊 FINAL COMPARISON WITH UI:")
    print("="*100)
    
    # Check individual items
    matches = 0
    total_items = 0
    
    for ui_name, ui_val in ui_values.items():
        if ui_name in ['BEVERAGES', 'LIQUOR']:
            continue
        
        total_items += 1
        search_name = 'APERITIF' if ui_name == 'APPETITE' else ui_name
        
        calc_key = None
        for key in ui_results.keys():
            if key.endswith(f"_{search_name}"):
                calc_key = key
                break
        
        if calc_key:
            calc_val = ui_results[calc_key]
            match = abs(ui_val - calc_val) < 1000
            if match:
                matches += 1
            print(f"{ui_name:<20}: UI={format_indian_number(ui_val):<15} "
                  f"Calc={format_indian_number(calc_val):<15} {'✅' if match else '❌'}")
        else:
            print(f"{ui_name:<20}: UI={format_indian_number(ui_val):<15} "
                  f"Calc=NOT FOUND                {'❌'}")
    
    # Check category totals
    print(f"\n📊 CATEGORY TOTALS:")
    print("-" * 50)
    
    category_matches = 0
    for category in ['BEVERAGES', 'LIQUOR']:
        ui_val = ui_values[category]
        calc_val = category_totals.get(category, 0)
        match = abs(ui_val - calc_val) < 1000
        if match:
            category_matches += 1
        print(f"{category:<20}: UI={format_indian_number(ui_val):<15} "
              f"Calc={format_indian_number(calc_val):<15} {'✅' if match else '❌'}")
    
    # Zero-sum validation (only for cross-category items)
    cross_category_total = sum(cross_category_results.values())
    zero_sum_valid = abs(cross_category_total) < 0.01
    
    print(f"\n🎯 VALIDATION SUMMARY:")
    print("="*50)
    print(f"Individual items: {matches}/{total_items} ✅")
    print(f"Category totals: {category_matches}/2 ✅")
    print(f"Cross-category zero-sum: {'✅' if zero_sum_valid else '❌'}")
    print(f"Cross-category total: ₹{format_indian_number(cross_category_total)}")
    
    if matches == total_items and category_matches == 2 and zero_sum_valid:
        print(f"\n🎉 ALL ISSUES FIXED! Cross-category indents calculation is perfect!")
    else:
        print(f"\n⚠️  Some issues remain:")
        if matches < total_items:
            print(f"   - {total_items - matches} individual items don't match")
        if category_matches < 2:
            print(f"   - {2 - category_matches} category totals don't match")
        if not zero_sum_valid:
            print(f"   - Cross-category indents don't sum to zero")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Final Cross-Category Indent Validation

This script tests if the UI shows the final net cross-category indent values
after the bidirectional distribution logic is applied.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Test the final cross-category indent calculation."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*80)
    print("FINAL CROSS-CATEGORY INDENT VALIDATION")
    print("="*80)
    
    # UI values from screenshot
    ui_values = {
        'BEVERAGES': 743288,
        'CRUSH&SYRUPS': -78938,
        'SOFT DRINK': -186144,
        # Add more visible values from screenshot
    }
    
    # Calculate raw indent values by category/subcategory
    indent_values = {}
    
    for _, row in df.iterrows():
        category = row['Category']
        subcategory = row['Sub Category']
        workarea = row['WorkArea']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                key = f"{category}_{subcategory}"
                if key not in indent_values:
                    indent_values[key] = {'value': 0, 'workarea': workarea}
                indent_values[key]['value'] += indent_value
        except (ValueError, TypeError):
            continue
    
    # Category-workarea mappings
    category_workarea_mappings = {
        "BEVERAGES": ["BAR"],  # BAR is mapped to BEVERAGES (virtual)
        "FOOD": ["INDIAN-KITCHEN", "TANDOORI-KITCHEN", "THAI-KITCHEN"],
        "LIQUOR": ["BAR"],  # BAR is mapped to LIQUOR (virtual)
        "SMOKE": ["BAR"]  # BAR is mapped to SMOKE (virtual)
    }
    
    # Apply cross-category logic
    final_results = {}
    
    print(f"\n🔍 APPLYING CROSS-CATEGORY LOGIC:")
    print("-" * 60)
    
    for key, data in indent_values.items():
        category, subcategory = key.split('_', 1)
        workarea = data['workarea']
        indent_value = data['value']
        
        # Determine workarea owners
        workarea_owners = []
        for cat, areas in category_workarea_mappings.items():
            if workarea in areas:
                workarea_owners.append(cat)
        
        print(f"{category}>{subcategory} in {workarea} (₹{format_indian_number(indent_value)})")
        print(f"  Workarea owners: {workarea_owners}")
        
        # Check if cross-category
        if category in workarea_owners:
            cross_category_owners = [owner for owner in workarea_owners if owner != category]
            
            if cross_category_owners:
                # Cross-category: negative for giving, positive for receiving
                if key not in final_results:
                    final_results[key] = 0
                final_results[key] -= indent_value  # Negative for giving category
                
                print(f"  → Cross-category OUTFLOW: -{format_indian_number(indent_value)} from {category}")
                
                # Distribute to receiving categories
                distributed_value = indent_value / len(cross_category_owners)
                for receiving_cat in cross_category_owners:
                    receiving_key = f"{receiving_cat}_Goods from Other Categories' Indents"
                    if receiving_key not in final_results:
                        final_results[receiving_key] = 0
                    final_results[receiving_key] += distributed_value
                    
                    print(f"  → Cross-category INFLOW: +{format_indian_number(distributed_value)} to {receiving_cat}")
            else:
                # Same category - no cross-category effect
                print(f"  → Same category indent (no cross-category effect)")
        else:
            print(f"  → Category {category} doesn't own {workarea} - treating as cross-category")
    
    # Calculate category totals
    print(f"\n📊 FINAL CATEGORY TOTALS:")
    print("-" * 50)
    
    category_totals = {}
    for key, value in final_results.items():
        category = key.split('_')[0]
        if category not in category_totals:
            category_totals[category] = 0
        category_totals[category] += value
    
    for category, total in category_totals.items():
        print(f"{category}: ₹{format_indian_number(total)}")
    
    # Compare with UI
    print(f"\n🎯 COMPARISON WITH UI:")
    print("-" * 50)
    
    beverages_calculated = category_totals.get('BEVERAGES', 0)
    beverages_ui = ui_values['BEVERAGES']
    
    print(f"BEVERAGES:")
    print(f"  UI: ₹{format_indian_number(beverages_ui)}")
    print(f"  Calculated: ₹{format_indian_number(beverages_calculated)}")
    print(f"  Difference: ₹{format_indian_number(abs(beverages_ui - beverages_calculated))}")
    
    # Check individual items
    print(f"\nINDIVIDUAL ITEMS:")
    crush_calculated = final_results.get('BEVERAGES_CRUSH&SYRUPS', 0)
    crush_ui = ui_values['CRUSH&SYRUPS']
    
    print(f"CRUSH&SYRUPS:")
    print(f"  UI: ₹{format_indian_number(crush_ui)}")
    print(f"  Calculated: ₹{format_indian_number(crush_calculated)}")
    
    soft_calculated = final_results.get('BEVERAGES_SOFT DRINK', 0)
    soft_ui = ui_values['SOFT DRINK']
    
    print(f"SOFT DRINK:")
    print(f"  UI: ₹{format_indian_number(soft_ui)}")
    print(f"  Calculated: ₹{format_indian_number(soft_calculated)}")
    
    print(f"\n💡 CONCLUSION:")
    print("-" * 30)
    
    if abs(beverages_ui - beverages_calculated) < 1000:
        print("✅ The calculation logic matches the UI!")
        print("   The codebase is working correctly.")
    elif abs(crush_ui - crush_calculated) < 1000:
        print("✅ Individual item calculations match!")
        print("   The UI shows item-level cross-category indents.")
    else:
        print("❌ Still no exact match found.")
        print("   There might be additional logic or data processing.")
        print("   Recommendation: Check if there are additional filters or")
        print("   transformations applied in the UI that aren't in the CSV.")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Final Debug Individual Items

This script checks why individual item calculations don't match.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Debug individual item calculations."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*80)
    print("FINAL DEBUG: INDIVIDUAL ITEM CALCULATIONS")
    print("="*80)
    
    # Check CRUSH&SYRUPS specifically
    crush_data = df[df['Sub Category'] == 'CRUSH&SYRUPS'].copy()
    print(f"\n🔍 CRUSH&SYRUPS DETAILED ANALYSIS:")
    print("-" * 60)
    print(f"Total rows: {len(crush_data)}")
    
    crush_total = 0
    for _, row in crush_data.iterrows():
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                print(f"  {row['Item Name']}: Qty={indent_qty} * Price={unit_price} = ₹{format_indian_number(indent_value)}")
                crush_total += indent_value
        except (ValueError, TypeError):
            continue
    
    print(f"CRUSH&SYRUPS Total: ₹{format_indian_number(crush_total)}")
    print(f"UI shows: -₹78,938 (negative)")
    print(f"Expected: ₹78,938.36 (positive raw value)")
    
    # Check SOFT DRINK specifically
    soft_data = df[df['Sub Category'] == 'SOFT DRINK'].copy()
    print(f"\n🔍 SOFT DRINK DETAILED ANALYSIS:")
    print("-" * 60)
    print(f"Total rows: {len(soft_data)}")
    
    soft_total = 0
    for _, row in soft_data.iterrows():
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                print(f"  {row['Item Name']}: Qty={indent_qty} * Price={unit_price} = ₹{format_indian_number(indent_value)}")
                soft_total += indent_value
        except (ValueError, TypeError):
            continue
    
    print(f"SOFT DRINK Total: ₹{format_indian_number(soft_total)}")
    print(f"UI shows: -₹1,86,144 (negative)")
    print(f"Expected: ₹1,86,144.25 (positive raw value)")
    
    # The issue might be that the UI is showing the NET cross-category indents
    # Let me calculate what BEVERAGES should show after cross-category distribution
    
    print(f"\n🎯 CROSS-CATEGORY CALCULATION:")
    print("-" * 50)
    
    # BEVERAGES gives away (negative):
    beverages_outflow = crush_total + soft_total
    print(f"BEVERAGES outflow: -₹{format_indian_number(beverages_outflow)}")
    
    # BEVERAGES receives from other categories in BAR (positive):
    # From my previous calculation: ₹81,530.77 (after receiving from FOOD, GENERAL, etc.)
    beverages_inflow = 81530.77 + beverages_outflow  # Add back what we subtracted
    print(f"BEVERAGES inflow: +₹{format_indian_number(beverages_inflow)}")
    
    # Net BEVERAGES
    net_beverages = beverages_inflow - beverages_outflow
    print(f"Net BEVERAGES: ₹{format_indian_number(net_beverages)}")
    
    # Compare with UI
    ui_beverages = 743288
    print(f"\nUI shows: ₹{format_indian_number(ui_beverages)}")
    print(f"Calculated net: ₹{format_indian_number(net_beverages)}")
    print(f"Difference: ₹{format_indian_number(abs(ui_beverages - net_beverages))}")
    
    if abs(ui_beverages - net_beverages) < 1000:
        print("✅ PERFECT MATCH!")
    else:
        print("❌ Still not matching.")
        
        # Maybe the UI is showing raw indent values without cross-category logic?
        raw_beverages_total = crush_total + soft_total
        print(f"\nRaw BEVERAGES total (no cross-category): ₹{format_indian_number(raw_beverages_total)}")
        print(f"UI shows: ₹{format_indian_number(ui_beverages)}")
        print(f"Difference: ₹{format_indian_number(abs(ui_beverages - raw_beverages_total))}")
        
        if abs(ui_beverages - raw_beverages_total) < 1000:
            print("✅ UI shows RAW indent values, not cross-category indents!")
            print("   This means the codebase might not be applying cross-category logic correctly.")

if __name__ == "__main__":
    main()

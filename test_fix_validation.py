#!/usr/bin/env python3
"""
Test Fix Validation

This script tests if the fix resolves the BEVERAGES category total issue.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Test the fix."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*80)
    print("TESTING FIX FOR BEVERAGES CATEGORY TOTAL")
    print("="*80)
    
    # Expected UI value
    ui_beverages_total = 743288
    
    # Category-workarea mappings (corrected logic)
    category_workarea_mappings = {
        "BEVERAGES": ["BAR"],
        "FOOD": ["INDIAN-KITCHEN", "TANDOORI-KITCHEN", "THAI-KITCHEN"],
        "LIQUOR": ["BAR"],
        "SMOKE": ["BAR"]
    }
    
    # Calculate cross-category indents with CORRECTED logic
    cross_category_results = {}
    
    # Calculate all indent values by workarea and category
    workarea_indents = {}
    
    for _, row in df.iterrows():
        workarea = row['WorkArea']
        category = row['Category']
        subcategory = row['Sub Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if workarea not in workarea_indents:
                    workarea_indents[workarea] = {}
                if category not in workarea_indents[workarea]:
                    workarea_indents[workarea][category] = {}
                if subcategory not in workarea_indents[workarea][category]:
                    workarea_indents[workarea][category][subcategory] = 0
                workarea_indents[workarea][category][subcategory] += indent_value
        except (ValueError, TypeError):
            continue
    
    # Process each workarea
    for workarea, workarea_categories in workarea_indents.items():
        # Determine workarea owners
        workarea_owners = []
        for cat, areas in category_workarea_mappings.items():
            if workarea in areas:
                workarea_owners.append(cat)
        
        print(f"\n🏭 Processing {workarea} (owners: {workarea_owners}):")
        
        # Process each category's indents in this workarea
        for indent_category, subcategories in workarea_categories.items():
            for subcategory, indent_value in subcategories.items():
                # CORRECTED LOGIC: Check if indent category doesn't own this workarea
                is_cross_category = indent_category not in workarea_owners
                
                if not is_cross_category:
                    print(f"   {indent_category}>{subcategory}: ₹{format_indian_number(indent_value)} (same category)")
                    continue
                
                print(f"   {indent_category}>{subcategory}: ₹{format_indian_number(indent_value)} (CROSS-CATEGORY)")
                
                # Initialize results
                key = f"{indent_category}_{subcategory}"
                if key not in cross_category_results:
                    cross_category_results[key] = 0
                
                # Negative for giving category
                cross_category_results[key] -= indent_value
                
                # Positive for receiving categories (workarea owners)
                for receiving_category in workarea_owners:
                    receiving_key = f"{receiving_category}_Goods from Other Categories' Indents"
                    if receiving_key not in cross_category_results:
                        cross_category_results[receiving_key] = 0
                    
                    distributed_value = indent_value / len(workarea_owners)
                    cross_category_results[receiving_key] += distributed_value
                    
                    print(f"      → +₹{format_indian_number(distributed_value)} to {receiving_category}")
    
    # Calculate category totals
    category_totals = {}
    for key, value in cross_category_results.items():
        category = key.split('_')[0]
        if category not in category_totals:
            category_totals[category] = 0
        category_totals[category] += value
    
    print(f"\n📊 FINAL CATEGORY TOTALS (WITH FIX):")
    print("-" * 50)
    for category, total in category_totals.items():
        print(f"{category}: ₹{format_indian_number(total)}")
    
    # Check BEVERAGES specifically
    beverages_calculated = category_totals.get('BEVERAGES', 0)
    
    print(f"\n🎯 BEVERAGES COMPARISON:")
    print("-" * 40)
    print(f"UI shows: ₹{format_indian_number(ui_beverages_total)}")
    print(f"Fixed calculation: ₹{format_indian_number(beverages_calculated)}")
    print(f"Difference: ₹{format_indian_number(abs(ui_beverages_total - beverages_calculated))}")
    
    if abs(ui_beverages_total - beverages_calculated) < 1000:
        print("✅ FIX SUCCESSFUL! The calculation now matches the UI.")
    else:
        print("❌ Fix didn't resolve the issue completely.")
    
    # Verify zero-sum
    total_cross_indents = sum(category_totals.values())
    print(f"\n🎯 ZERO-SUM VALIDATION:")
    print(f"Total cross-category indents: ₹{format_indian_number(total_cross_indents)}")
    
    if abs(total_cross_indents) < 0.01:
        print("✅ Zero-sum validation passed!")
    else:
        print("⚠️  Zero-sum validation failed!")

if __name__ == "__main__":
    main()

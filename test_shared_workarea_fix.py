#!/usr/bin/env python3
"""
Test Shared Workarea Fix

This script tests if treating shared workareas as cross-category resolves the issue.
"""

import pandas as pd

def format_indian_number(value: float) -> str:
    """Format numbers in Indian decimal/comma separation format."""
    if pd.isna(value) or value == 0:
        return "0"
    
    is_negative = value < 0
    abs_value = abs(value)
    formatted = f"{abs_value:,.2f}"
    
    parts = formatted.split('.')
    integer_part = parts[0].replace(',', '')
    decimal_part = parts[1] if len(parts) > 1 else "00"
    
    if len(integer_part) > 3:
        last_three = integer_part[-3:]
        remaining = integer_part[:-3]
        
        formatted_remaining = ""
        for i, digit in enumerate(reversed(remaining)):
            if i > 0 and i % 2 == 0:
                formatted_remaining = "," + formatted_remaining
            formatted_remaining = digit + formatted_remaining
        
        result = formatted_remaining + "," + last_three + "." + decimal_part
    else:
        result = integer_part + "." + decimal_part
    
    return ("-" if is_negative else "") + result

def main():
    """Test the shared workarea fix."""
    
    try:
        df = pd.read_csv('inventory_consumption_df.csv')
        print(f"✓ Loaded CSV data: {len(df)} rows")
    except Exception as e:
        print(f"✗ Error loading CSV: {e}")
        return
    
    print("\n" + "="*80)
    print("TESTING SHARED WORKAREA FIX")
    print("="*80)
    
    # Expected UI values
    ui_values = {
        'BEVERAGES': 743288,
        'CRUSH&SYRUPS': -78938,
        'SOFT DRINK': -186144
    }
    
    # Category-workarea mappings
    category_workarea_mappings = {
        "BEVERAGES": ["BAR"],
        "FOOD": ["INDIAN-KITCHEN", "TANDOORI-KITCHEN", "THAI-KITCHEN"],
        "LIQUOR": ["BAR"],
        "SMOKE": ["BAR"]
    }
    
    # Calculate cross-category indents with SHARED WORKAREA logic
    cross_category_results = {}
    
    # Step 1: Calculate all indent values by workarea and category
    workarea_indents = {}
    
    for _, row in df.iterrows():
        workarea = row['WorkArea']
        category = row['Category']
        subcategory = row['Sub Category']
        
        try:
            unit_price = float(row.get('WAC(incl.tax,etc)', 0) or 0)
            indent_qty = float(row.get('WorkArea Indent', 0) or 0)
            indent_value = indent_qty * unit_price if unit_price > 0 else 0
            
            if abs(indent_value) > 0.01:
                if workarea not in workarea_indents:
                    workarea_indents[workarea] = {}
                if category not in workarea_indents[workarea]:
                    workarea_indents[workarea][category] = {}
                if subcategory not in workarea_indents[workarea][category]:
                    workarea_indents[workarea][category][subcategory] = 0
                workarea_indents[workarea][category][subcategory] += indent_value
        except (ValueError, TypeError):
            continue
    
    # Step 2: Process cross-category logic with SHARED WORKAREA fix
    for workarea, workarea_categories in workarea_indents.items():
        # Determine workarea owners
        workarea_owner_categories = []
        for cat, areas in category_workarea_mappings.items():
            if workarea in areas:
                workarea_owner_categories.append(cat)
        
        print(f"\n🏭 Processing {workarea} (owners: {workarea_owner_categories}):")
        
        # Process each category's indents in this workarea
        for indent_category, subcategories in workarea_categories.items():
            for subcategory, indent_value in subcategories.items():
                # FIXED LOGIC: Check if this is cross-category
                # 1. Indent category doesn't own this workarea, OR
                # 2. Workarea is shared by multiple categories (like BAR)
                is_cross_category = (indent_category not in workarea_owner_categories or 
                                   len(workarea_owner_categories) > 1)
                
                if not is_cross_category:
                    print(f"   {indent_category}>{subcategory}: ₹{format_indian_number(indent_value)} (same category)")
                    continue
                
                print(f"   {indent_category}>{subcategory}: ₹{format_indian_number(indent_value)} (CROSS-CATEGORY)")
                
                # Initialize results
                key = f"{indent_category}_{subcategory}"
                if key not in cross_category_results:
                    cross_category_results[key] = 0
                
                # Negative for giving category
                cross_category_results[key] -= indent_value
                
                # Positive for receiving categories (workarea owners)
                for workarea_owner_category in workarea_owner_categories:
                    receiving_key = f"{workarea_owner_category}_Goods from Other Categories' Indents"
                    if receiving_key not in cross_category_results:
                        cross_category_results[receiving_key] = 0
                    
                    distributed_value = indent_value / len(workarea_owner_categories)
                    cross_category_results[receiving_key] += distributed_value
                    
                    print(f"      → +₹{format_indian_number(distributed_value)} to {workarea_owner_category}")
    
    # Calculate category totals
    category_totals = {}
    for key, value in cross_category_results.items():
        category = key.split('_')[0]
        if category not in category_totals:
            category_totals[category] = 0
        category_totals[category] += value
    
    print(f"\n📊 FINAL RESULTS:")
    print("-" * 50)
    
    # Show individual items
    print("Individual items:")
    for key, value in cross_category_results.items():
        if abs(value) > 0.01 and 'Goods from Other Categories' not in key:
            category, subcategory = key.split('_', 1)
            print(f"  {category}>{subcategory}: ₹{format_indian_number(value)}")
    
    # Show category totals
    print(f"\nCategory totals:")
    for category, total in category_totals.items():
        if abs(total) > 0.01:
            print(f"  {category}: ₹{format_indian_number(total)}")
    
    # Compare with UI
    print(f"\n🎯 COMPARISON WITH UI:")
    print("-" * 40)
    
    beverages_calculated = category_totals.get('BEVERAGES', 0)
    crush_calculated = cross_category_results.get('BEVERAGES_CRUSH&SYRUPS', 0)
    soft_calculated = cross_category_results.get('BEVERAGES_SOFT DRINK', 0)
    
    print(f"BEVERAGES total:")
    print(f"  UI: ₹{format_indian_number(ui_values['BEVERAGES'])}")
    print(f"  Calculated: ₹{format_indian_number(beverages_calculated)}")
    print(f"  Difference: ₹{format_indian_number(abs(ui_values['BEVERAGES'] - beverages_calculated))}")
    
    print(f"\nCRUSH&SYRUPS:")
    print(f"  UI: ₹{format_indian_number(ui_values['CRUSH&SYRUPS'])}")
    print(f"  Calculated: ₹{format_indian_number(crush_calculated)}")
    print(f"  Match: {abs(ui_values['CRUSH&SYRUPS'] - crush_calculated) < 1000}")
    
    print(f"\nSOFT DRINK:")
    print(f"  UI: ₹{format_indian_number(ui_values['SOFT DRINK'])}")
    print(f"  Calculated: ₹{format_indian_number(soft_calculated)}")
    print(f"  Match: {abs(ui_values['SOFT DRINK'] - soft_calculated) < 1000}")
    
    # Verify zero-sum
    total_cross_indents = sum(category_totals.values())
    print(f"\n🎯 ZERO-SUM VALIDATION:")
    print(f"Total cross-category indents: ₹{format_indian_number(total_cross_indents)}")
    
    if abs(total_cross_indents) < 0.01:
        print("✅ Zero-sum validation passed!")
    else:
        print("⚠️  Zero-sum validation failed!")
    
    # Final assessment
    individual_items_match = (abs(ui_values['CRUSH&SYRUPS'] - crush_calculated) < 1000 and
                             abs(ui_values['SOFT DRINK'] - soft_calculated) < 1000)
    
    if individual_items_match:
        print("\n🎉 SUCCESS! Individual item calculations now match the UI!")
        if abs(ui_values['BEVERAGES'] - beverages_calculated) < 1000:
            print("🎉 Category total also matches!")
        else:
            print("⚠️  Category total still needs adjustment.")
    else:
        print("\n❌ Individual items still don't match.")

if __name__ == "__main__":
    main()
